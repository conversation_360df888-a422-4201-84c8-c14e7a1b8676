# Bangladesh Railway Ticket Booking Automation

This project contains automated tests for the Bangladesh Railway ticket booking system using Playwright.

## Prerequisites

- Node.js 16 or higher
- npm or yarn

## Setup

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npx playwright install
```

3. Create a `.env` file in the root directory and add your credentials and preferences:
```
RAILWAY_USERNAME=your_username
RAILWAY_PASSWORD=your_password

# Search Parameters
SEARCH_FROM=Dhaka
SEARCH_TO=Joydebpur
SEARCH_DATE=2025-05-28
SEARCH_CLASS=S_CHAIR

# Booking Parameters
TRAIN_NAME=DHUMKETU EXPRESS
PREFERRED_SEATS=A1,A2,B1,B2
NUMBER_OF_SEATS=1
```

## Running Tests

To run tests in headed mode:
```bash
npm test
```

To run tests with UI mode:
```bash
npm run test:ui
```

## Test Description

The test automation uses API-based testing to avoid UI-related issues and provide more reliable testing:

### Test Case 1: Authentication API Testing
- Tests various common login API endpoints (/api/auth/login, /api/login, etc.)
- Attempts authentication with mobile number and password
- Verifies API endpoints exist and respond appropriately
- Handles different response formats and authentication methods

### Test Case 2: Search API Endpoint Discovery
- Tests access to search-related API endpoints
- Checks for endpoints like /api/search, /api/trains/search, /api/stations
- Verifies endpoints are accessible and respond with appropriate status codes
- Identifies which endpoints require authentication

### Test Case 3: Train Search API Testing
- Performs actual train search requests via API
- Tests search with parameters (from: Dhaka, to: Rajshahi, class: SNIGDHA)
- Tries both POST and GET methods with search data
- Verifies search functionality through API calls

### Test Case 4: User Profile API Testing
- Tests access to user profile API endpoints
- Checks endpoints like /api/user/profile, /api/profile, /api/me
- Verifies profile-related functionality is accessible via API

### Test Case 5: Booking API Endpoint Discovery
- Tests access to booking-related API endpoints
- Checks endpoints like /api/booking, /api/tickets, /api/reservations
- Verifies booking functionality endpoints exist and are accessible

### Test Case 6: Complete API Booking Flow
- Performs end-to-end ticket booking using API calls only
- Reads search and booking parameters from .env file
- Attempts authentication via multiple API endpoints
- Searches for trains using configured parameters (from, to, date, class)
- Looks for specific train by name (configurable via TRAIN_NAME)
- Attempts booking via multiple booking API endpoints
- Includes seat selection preferences (configurable via PREFERRED_SEATS)
- **Smart Fallback Logic**: If preferred seats are not available, automatically tries:
  - Generic seat patterns based on NUMBER_OF_SEATS
  - Common seat naming conventions
  - Seat count-only booking requests
- Provides comprehensive API request/response logging
- Tests booking endpoints even when search results are not available

## Configuration

All test parameters are configurable via the `.env` file:

- **SEARCH_FROM**: Origin station (default: Dhaka)
- **SEARCH_TO**: Destination station (default: Joydebpur)
- **SEARCH_DATE**: Journey date in YYYY-MM-DD format (default: 2025-05-28)
- **SEARCH_CLASS**: Train class preference (default: S_CHAIR)
- **TRAIN_NAME**: Specific train to book (default: DHUMKETU EXPRESS)
- **PREFERRED_SEATS**: Comma-separated list of preferred seat numbers (default: A1,A2,B1,B2)
- **NUMBER_OF_SEATS**: Number of seats to book if preferred seats are not available (default: 1)

## Notes

- **API-Based Testing**: Tests use HTTP requests instead of UI automation for better reliability
- **Endpoint Discovery**: Tests automatically discover and verify API endpoints
- **Authentication Testing**: Comprehensive testing of various login API patterns
- **Error Handling**: Tests handle different HTTP status codes and response formats
- **No UI Dependencies**: API tests are not affected by UI changes or JavaScript loading issues
- **Faster Execution**: API tests run much faster than UI-based tests
- **Better Debugging**: Clear API request/response logging for easier troubleshooting
- **Cross-Platform**: API tests work consistently across different environments
- **Configurable Parameters**: All search and booking preferences can be customized via .env file
- **Complete API Coverage**: Tests cover authentication, search, and booking via API endpoints
- **Intelligent Endpoint Discovery**: Automatically discovers and tests multiple API endpoint patterns