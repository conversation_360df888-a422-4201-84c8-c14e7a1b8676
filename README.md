# Bangladesh Railway Ticket Booking Automation

This project contains automated tests for the Bangladesh Railway ticket booking system using Playwright.

## Prerequisites

- Node.js 16 or higher
- npm or yarn

## Setup

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npx playwright install
```

3. Create a `.env` file in the root directory and add your credentials:
```
RAILWAY_USERNAME=your_username
RAILWAY_PASSWORD=your_password
```

## Running Tests

To run tests in headed mode:
```bash
npm test
```

To run tests with UI mode:
```bash
npm run test:ui
```

## Test Description

The test automation performs the following steps:
1. Logs in to the Bangladesh Railway website
2. Handles the "I AGREE" button if present
3. Searches for tickets from Dhaka to Rajshahi
4. Looks for available SNIGDHA class tickets
5. Clicks the "Book Now" button if tickets are available

## Notes

- The test is configured to run in headed mode by default
- Screenshots and videos are captured on test failure
- Test reports are generated in HTML format 