# Bangladesh Railway Ticket Booking Automation

This project contains automated tests for the Bangladesh Railway ticket booking system using Playwright.

## Prerequisites

- Node.js 16 or higher
- npm or yarn

## Setup

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npx playwright install
```

3. Create a `.env` file in the root directory and add your credentials:
```
RAILWAY_USERNAME=your_username
RAILWAY_PASSWORD=your_password
```

## Running Tests

To run tests in headed mode:
```bash
npm test
```

To run tests with UI mode:
```bash
npm run test:ui
```

## Test Description

The test automation includes multiple test cases:

### Test Case 1: Terms and Conditions Acceptance
- Navigates to the Bangladesh Railway homepage
- Verifies the Terms and Conditions disclaimer dialog appears
- Clicks the "I AGREE" button to accept terms
- Verifies the dialog is dismissed

### Test Case 2: Login Flow
- Accepts Terms and Conditions first
- Navigates to the login page via "My Account"
- Fills in mobile number and password
- Logs in successfully and verifies profile page

### Test Case 3: SNIGDHA Class Ticket Search
- Accepts Terms and Conditions
- Logs in to the website
- Searches for tickets from Dhaka to Rajshahi
- Looks for available SNIGDHA class tickets
- Clicks the "Book Now" button if tickets are available

### Test Case 4: Complete End-to-End Flow
- Accepts Terms and Conditions
- Logs in to the website
- Uses the search form to find tickets
- Attempts to book available tickets
- Handles both success and no-availability scenarios

## Notes

- The test is configured to run in headed mode by default
- Screenshots and videos are captured on test failure
- Test reports are generated in HTML format