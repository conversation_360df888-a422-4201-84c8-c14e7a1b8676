# Bangladesh Railway Ticket Booking Automation

This project contains automated tests for the Bangladesh Railway ticket booking system using Playwright.

## Prerequisites

- Node.js 16 or higher
- npm or yarn

## Setup

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npx playwright install
```

3. Create a `.env` file in the root directory and add your credentials:
```
RAILWAY_USERNAME=your_username
RAILWAY_PASSWORD=your_password
```

## Running Tests

To run tests in headed mode:
```bash
npm test
```

To run tests with UI mode:
```bash
npm run test:ui
```

## Test Description

The test automation includes multiple test cases based on the actual website flow:

### Test Case 1: Login Functionality
- Navigates to the Bangladesh Railway website (redirects to login)
- Fills in mobile number and password credentials
- Logs in successfully and verifies authentication
- Handles Terms and Conditions dialog if it appears after login

### Test Case 2: Navigation to Ticket Search
- Logs in to the website first
- Attempts to navigate to ticket search functionality
- Verifies access to search form elements (From, To, Journey Date, Class)
- Confirms search functionality is accessible after authentication

### Test Case 3: Train Ticket Search
- Logs in to the website
- Navigates to search page or finds search form
- Verifies search elements are available and functional
- Tests access to booking-related pages
- Handles cases where search functionality may not be immediately visible

### Test Case 4: User Profile Access
- Logs in to the website
- Navigates to user profile page
- Verifies profile information is accessible
- Checks for profile elements like Name, Email, Mobile, Edit Profile, Log Out

## Notes

- The website requires login before accessing any booking functionality
- The test is configured to run in headed mode by default
- Screenshots and videos are captured on test failure
- Test reports are generated in HTML format
- All tests include proper error handling and fallback strategies
- Tests are designed to be resilient to UI changes and different page states