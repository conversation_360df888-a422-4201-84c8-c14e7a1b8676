import { expect, test } from '@playwright/test';
import * as dotenv from 'dotenv';

dotenv.config();

test.describe('Bangladesh Railway Ticket Booking', () => {
    test('should be able to search and book SNIGDHA class tickets', async ({ page }) => {
        // Login
        await page.goto('https://railapp.railway.gov.bd');
        await page.waitForLoadState('networkidle');
        
        const username = process.env.RAILWAY_USERNAME;
        const password = process.env.RAILWAY_PASSWORD;
        
        if (!username || !password) {
            throw new Error('RAILWAY_USERNAME and RAILWAY_PASSWORD must be set in .env file');
        }
        
        await page.fill('input[name="username"]', username);
        await page.fill('input[type="password"]', password);
        await page.click('button[type="submit"]');
        
        // Wait for login to complete and redirect
        await page.waitForURL('https://railapp.railway.gov.bd/auth/login');
        
        // Handle "I AGREE" button if present
        try {
            const agreeButton = await page.getByText('I AGREE', { exact: true });
            if (await agreeButton.isVisible()) {
                await agreeButton.click();
            }
        } catch (error) {
            console.log('I AGREE button not found, continuing...');
        }
        
        // Navigate to search page with parameters
        await page.goto('https://railapp.railway.gov.bd/search?fromcity=Dhaka&tocity=Rajshahi&doj=06-Jun-2025&class=SNIGDHA');
        await page.waitForLoadState('networkidle');
        
        // Find available SNIGDHA class tickets
        const rows = await page.locator('tr').filter({
            has: page.locator('td:has-text("SNIGDHA")')
        }).all();
        
        let bookingFound = false;
        
        for (const row of rows) {
            const availableTickets = await row.locator('td').filter({
                hasText: /Available Tickets/
            }).innerText();
            
            const ticketCount = parseInt(availableTickets.match(/\d+/)?.[0] || '0');
            
            if (ticketCount > 0) {
                const bookButton = row.getByRole('button', { name: /Book Now/i });
                await bookButton.click();
                bookingFound = true;
                break;
            }
        }
        
        if (!bookingFound) {
            console.log('No available SNIGDHA class tickets found');
        }
        
        // Add additional assertions as needed
        expect(bookingFound).toBeTruthy();
    });
}); 