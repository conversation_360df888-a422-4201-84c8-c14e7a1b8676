import { expect, Page, test } from '@playwright/test';
import * as dotenv from 'dotenv';

dotenv.config();

// Helper function to perform login
async function loginToRailway(page: Page) {
    const username = process.env.RAILWAY_USERNAME;
    const password = process.env.RAILWAY_PASSWORD;

    if (!username || !password) {
        throw new Error('RAILWAY_USERNAME and RAILWAY_PASSWORD must be set in .env file');
    }

    // Navigate to the website (will redirect to login)
    await page.goto('https://railapp.railway.gov.bd');

    // Wait for login page to load
    await page.waitForSelector('text=Welcome to Rail Sheba', { timeout: 15000 });

    // Fill in credentials
    await page.fill('textbox[placeholder*="Mobile Number"], input[placeholder*="Mobile Number"]', username);
    await page.fill('textbox[placeholder*="Password"], input[type="password"]', password);

    // Click login button
    await page.click('button:has-text("LOGIN")');

    // Wait for successful login - could redirect to profile or homepage
    await page.waitForLoadState('domcontentloaded', { timeout: 15000 });

    console.log(`After login, current URL: ${page.url()}`);

    // Handle Terms and Conditions dialog if it appears after login
    const disclaimerDialog = page.locator('dialog');
    if (await disclaimerDialog.isVisible({ timeout: 5000 })) {
        console.log('Terms and Conditions dialog found after login');
        const agreeButton = disclaimerDialog.locator('button:has-text("I AGREE")');
        await agreeButton.click();
        await expect(disclaimerDialog).not.toBeVisible();
        console.log('Terms and Conditions accepted');
    }
}

test.describe('Bangladesh Railway Ticket Booking', () => {

    test('should be able to login successfully', async ({ page }) => {
        // Use helper function to login
        await loginToRailway(page);

        // Verify successful login by checking we're not on login page anymore
        expect(page.url()).not.toContain('/auth/login');

        // Should be on profile page or homepage after login
        const isOnProfile = page.url().includes('/profile');
        const isOnHome = page.url() === 'https://railapp.railway.gov.bd/';

        expect(isOnProfile || isOnHome).toBeTruthy();
    });

    test('should be able to navigate to ticket search after login', async ({ page }) => {
        // Login first
        await loginToRailway(page);

        // Navigate to Buy Tickets or search page
        // Check if we can find Buy Tickets button/link
        const buyTicketsButton = page.locator('text=Buy Tickets, a[href*="search"], button:has-text("Buy Tickets")').first();

        if (await buyTicketsButton.isVisible({ timeout: 5000 })) {
            await buyTicketsButton.click();
            await page.waitForLoadState('domcontentloaded');
        } else {
            // Try direct navigation to search page
            await page.goto('https://railapp.railway.gov.bd/search');
            await page.waitForLoadState('domcontentloaded');
        }

        // Verify we can access the search functionality
        const searchElements = [
            'text=From',
            'text=To',
            'text=Journey Date',
            'text=Class',
            'button:has-text("SEARCH")'
        ];

        let searchPageLoaded = false;
        for (const selector of searchElements) {
            if (await page.locator(selector).isVisible({ timeout: 3000 })) {
                searchPageLoaded = true;
                break;
            }
        }

        expect(searchPageLoaded).toBeTruthy();
    });

    test('should be able to search for train tickets', async ({ page }) => {
        // Login first
        await loginToRailway(page);

        // Navigate to search page or use search functionality
        // Try to access search page directly
        await page.goto('https://railapp.railway.gov.bd/search');
        await page.waitForLoadState('domcontentloaded');

        // If search page is not accessible, try to find search form on current page
        const searchForm = page.locator('form, [class*="search"], [id*="search"]').first();

        if (await searchForm.isVisible({ timeout: 5000 })) {
            console.log('Search form found on current page');
        } else {
            // Try to navigate via Buy Tickets button
            const buyTicketsButton = page.locator('text=Buy Tickets, a[href*="search"], button:has-text("Buy Tickets")').first();
            if (await buyTicketsButton.isVisible({ timeout: 5000 })) {
                await buyTicketsButton.click();
                await page.waitForLoadState('domcontentloaded');
            }
        }

        // Look for search elements
        const searchElements = [
            'text=From',
            'text=To',
            'text=Journey Date',
            'text=Class',
            'input[placeholder*="From"]',
            'input[placeholder*="To"]',
            'select[name*="from"]',
            'select[name*="to"]'
        ];

        let searchElementFound = false;
        for (const selector of searchElements) {
            if (await page.locator(selector).isVisible({ timeout: 3000 })) {
                console.log(`Found search element: ${selector}`);
                searchElementFound = true;
                break;
            }
        }

        // If we can access search functionality, verify it works
        if (searchElementFound) {
            console.log('Search functionality is accessible');
            expect(searchElementFound).toBeTruthy();
        } else {
            console.log('Search functionality not found, checking if we can access any booking features');

            // Check if we can at least access some booking-related pages
            const bookingPages = [
                'https://railapp.railway.gov.bd/search',
                'https://railapp.railway.gov.bd/booking',
                'https://railapp.railway.gov.bd/tickets'
            ];

            let accessiblePage = false;
            for (const url of bookingPages) {
                try {
                    await page.goto(url);
                    await page.waitForLoadState('domcontentloaded');

                    // Check if we're not redirected to login or error page
                    if (!page.url().includes('/auth/login') && !page.url().includes('/error')) {
                        accessiblePage = true;
                        console.log(`Successfully accessed: ${url}`);
                        break;
                    }
                } catch (error) {
                    console.log(`Could not access ${url}:`, error);
                    continue;
                }
            }

            expect(accessiblePage).toBeTruthy();
        }
    });

    test('should be able to access user profile', async ({ page }) => {
        // Login first
        await loginToRailway(page);

        // Try to navigate to profile page
        const profileButton = page.locator('text=My Account, text=Profile, a[href*="profile"]').first();

        if (await profileButton.isVisible({ timeout: 5000 })) {
            await profileButton.click();
            await page.waitForLoadState('domcontentloaded');
        } else {
            // Try direct navigation
            await page.goto('https://railapp.railway.gov.bd/profile');
            await page.waitForLoadState('domcontentloaded');
        }

        // Verify we can access profile information
        const profileElements = [
            'text=Name',
            'text=Email',
            'text=Mobile',
            'text=Profile',
            'text=EDIT PROFILE',
            'text=Log Out'
        ];

        let profileLoaded = false;
        for (const selector of profileElements) {
            if (await page.locator(selector).isVisible({ timeout: 3000 })) {
                console.log(`Found profile element: ${selector}`);
                profileLoaded = true;
                break;
            }
        }

        expect(profileLoaded).toBeTruthy();
    });
});